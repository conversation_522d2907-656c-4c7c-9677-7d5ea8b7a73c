import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';

const router = express.Router();

/**
 * 创建订单
 */
router.post('/create', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { itemId, amount, paymentMethod, itemDescription } = req.body;

    // 验证必填字段
    if (!itemId || !amount || !paymentMethod) {
      return res.status(400).json({ 
        success: false,
        message: '缺少必要的订单信息' 
      });
    }

    // 验证金额格式
    const orderAmount = parseFloat(amount);
    if (isNaN(orderAmount) || orderAmount <= 0) {
      return res.status(400).json({ 
        success: false,
        message: '订单金额无效' 
      });
    }

    // 验证支付方式
    if (!['ALIPAY', 'WECHATPAY'].includes(paymentMethod)) {
      return res.status(400).json({ 
        success: false,
        message: '不支持的支付方式' 
      });
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        userId: userId,
        itemId: itemId,
        amount: orderAmount,
        paymentMethod: paymentMethod,
        itemDescription: itemDescription || '',
        status: 'PENDING',
      },
    });

    return res.status(201).json({
      success: true,
      orderId: order.id,
      message: '订单创建成功',
      order: {
        id: order.id,
        amount: order.amount,
        status: order.status,
        paymentMethod: order.paymentMethod,
        itemDescription: order.itemDescription,
        createdAt: order.createdAt,
      }
    });

  } catch (error: any) {
    console.error('创建订单失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '创建订单失败，请稍后再试' 
    });
  }
});

/**
 * 获取用户订单列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const orders = await prisma.order.findMany({
      where: { userId: userId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        amount: true,
        status: true,
        paymentMethod: true,
        itemDescription: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return res.status(200).json({
      success: true,
      orders: orders
    });

  } catch (error: any) {
    console.error('获取订单列表失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取订单列表失败，请稍后再试' 
    });
  }
});

/**
 * 获取单个订单详情
 */
router.get('/:orderId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({ 
        success: false,
        message: '订单ID无效' 
      });
    }

    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: userId, // 确保用户只能查看自己的订单
      },
      select: {
        id: true,
        amount: true,
        status: true,
        paymentMethod: true,
        itemId: true,
        itemDescription: true,
        transactionId: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!order) {
      return res.status(404).json({ 
        success: false,
        message: '订单不存在' 
      });
    }

    return res.status(200).json({
      success: true,
      order: order
    });

  } catch (error: any) {
    console.error('获取订单详情失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取订单详情失败，请稍后再试' 
    });
  }
});

export default router;
