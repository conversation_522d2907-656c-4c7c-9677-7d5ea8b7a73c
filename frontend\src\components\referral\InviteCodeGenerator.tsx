import React, { useState } from 'react';
import { Co<PERSON>, Share2, MessageCircle, Send, Repeat, UserPlus, Check } from 'lucide-react';

// 邀请码接口
interface ReferralCode {
  code: string;
  inviteLink: string;
  createdAt: string;
  isActive: boolean;
  hasInviter: boolean; // 是否已有邀请人
}

// 分享平台类型
type SharePlatform = 'wechat' | 'qq' | 'weibo' | 'copy';

// 组件属性接口
interface InviteCodeGeneratorProps {
  referralCode: ReferralCode | null;
  onCopyCode: (code: string) => void;
  onCopyLink: (link: string) => void;
  onShare: (platform: SharePlatform, link: string) => void;
  onSetInviterCode?: (code: string) => Promise<void>;
  isLoading?: boolean;
}

/**
 * 格式化邀请链接显示
 */
const formatInviteLink = (link: string): string => {
  if (link.length <= 30) return link;

  // 提取邀请码（通常在URL最后）
  const codeMatch = link.match(/invite=([A-Z0-9]+)$/);
  const inviteCode = codeMatch ? codeMatch[1] : '';

  if (inviteCode.length >= 8) {
    // 显示前20个字符 + ... + 邀请码
    const prefix = link.substring(0, 20);
    return `${prefix}...${inviteCode}`;
  } else {
    // 如果没有找到邀请码，显示前20个字符 + ... + 后8个字符
    return `${link.substring(0, 20)}...${link.substring(link.length - 8)}`;
  }
};

/**
 * 邀请码生成组件
 */
const InviteCodeGenerator: React.FC<InviteCodeGeneratorProps> = ({
  referralCode,
  onCopyCode,
  onCopyLink,
  onShare,
  onSetInviterCode,
  isLoading = false
}) => {
  // 邀请人邀请码相关状态
  const [inviterCode, setInviterCode] = useState('');
  const [isSubmittingInviterCode, setIsSubmittingInviterCode] = useState(false);
  const [inviterCodeSubmitted, setInviterCodeSubmitted] = useState(false);

  // 处理设置邀请人邀请码
  const handleSetInviterCode = async () => {
    if (!inviterCode.trim() || !onSetInviterCode) return;

    setIsSubmittingInviterCode(true);
    try {
      await onSetInviterCode(inviterCode.trim());
      setInviterCodeSubmitted(true);
      setInviterCode('');
    } catch (error) {
      // 错误处理由父组件的onSetInviterCode处理
    } finally {
      setIsSubmittingInviterCode(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full flex flex-col">
        <div className="animate-pulse flex-1">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!referralCode) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full flex flex-col">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          我的邀请码
        </h2>

        {/* 邀请码区域 - 左右并列布局，紧凑间距 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
          {/* 邀请码显示区域 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              我的邀请码
            </label>
            <div className="flex items-center space-x-1">
              <div className="flex-1 px-2 py-1.5 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded">
                <span className="text-gray-400 dark:text-gray-500 text-sm">加载中...</span>
              </div>
              <button
                disabled
                className="px-2 py-1.5 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 rounded cursor-not-allowed"
              >
                <Copy className="w-3 h-3" />
              </button>
            </div>
          </div>

          {/* 邀请链接显示区域 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              邀请链接
            </label>
            <div className="flex items-center space-x-1">
              <div className="flex-1 px-2 py-1.5 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded">
                <span className="text-gray-400 dark:text-gray-500 text-sm">加载中...</span>
              </div>
              <button
                disabled
                className="px-2 py-1.5 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 rounded cursor-not-allowed"
              >
                <Copy className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>

        {/* 补填邀请码区域 - 紧凑间距 */}
        {onSetInviterCode && !inviterCodeSubmitted && (
          <div className="mb-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
            <div className="flex items-center gap-1 mb-1">
              <UserPlus className="w-3 h-3 text-blue-600 dark:text-blue-400" />
              <h3 className="text-xs font-medium text-blue-900 dark:text-blue-100">
                填写邀请人邀请码
              </h3>
            </div>
            <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">
              如果您是通过朋友邀请注册的，可以在这里填写邀请人的邀请码获得奖励
            </p>
            <div className="flex items-center space-x-1">
              <input
                type="text"
                value={inviterCode}
                onChange={(e) => setInviterCode(e.target.value.toUpperCase())}
                placeholder="请输入邀请人邀请码"
                className="flex-1 px-2 py-1.5 border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent text-xs"
                maxLength={10}
              />
              <button
                onClick={handleSetInviterCode}
                disabled={!inviterCode.trim() || isSubmittingInviterCode}
                className="px-2 py-1.5 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded transition-colors duration-200 flex items-center space-x-1 text-xs"
              >
                {isSubmittingInviterCode ? (
                  <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <Check className="w-3 h-3" />
                )}
                <span>{isSubmittingInviterCode ? '提交中...' : '提交'}</span>
              </button>
            </div>
          </div>
        )}


      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full flex flex-col">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
        我的邀请码
      </h2>

      {/* 填写邀请人邀请码区域 */}
      {onSetInviterCode && !referralCode.hasInviter && !inviterCodeSubmitted && (
        <div className="mb-4 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
          <div className="flex items-center gap-1 mb-1">
            <UserPlus className="w-3 h-3 text-blue-600 dark:text-blue-400" />
            <h3 className="text-xs font-medium text-blue-900 dark:text-blue-100">
              填写邀请人邀请码
            </h3>
          </div>
          <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">
            如果您是通过朋友邀请注册的，可以在这里填写邀请人的邀请码获得奖励
          </p>
          <div className="flex items-center space-x-1">
            <input
              type="text"
              value={inviterCode}
              onChange={(e) => setInviterCode(e.target.value.toUpperCase())}
              placeholder="请输入邀请人邀请码"
              className="flex-1 px-2 py-1.5 bg-white dark:bg-gray-700 border border-blue-300 dark:border-blue-600 rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400"
              disabled={isSubmittingInviterCode}
            />
            <button
              onClick={handleSetInviterCode}
              disabled={!inviterCode.trim() || isSubmittingInviterCode}
              className="px-2 py-1.5 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded transition-colors duration-200 text-xs flex items-center space-x-1"
            >
              {isSubmittingInviterCode ? (
                <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <UserPlus className="w-3 h-3" />
              )}
              <span>{isSubmittingInviterCode ? '提交中...' : '提交'}</span>
            </button>
          </div>
        </div>
      )}

      {/* 邀请人邀请码已提交提示 */}
      {inviterCodeSubmitted && (
        <div className="mb-4 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded">
          <div className="flex items-center gap-1">
            <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
            <span className="text-xs font-medium text-green-900 dark:text-green-100">
              邀请人邀请码已成功提交
            </span>
          </div>
        </div>
      )}

      {/* 邀请码和邀请链接区域 - 左右并列布局，紧凑设计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
        {/* 邀请码展示 */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            邀请码
          </label>
          <div className="flex items-center space-x-1">
            <div className="flex-1 px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded">
              <span className="text-base font-mono font-bold text-blue-600 dark:text-blue-400 tracking-wider">
                {referralCode.code}
              </span>
            </div>
            <button
              onClick={() => onCopyCode(referralCode.code)}
              className="px-2 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-200"
              title="复制邀请码"
            >
              <Copy className="w-3 h-3" />
            </button>
          </div>
        </div>

        {/* 邀请链接展示 */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            邀请链接
          </label>
          <div className="flex items-center space-x-1">
            <div className="flex-1 px-2 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded">
              <span className="text-xs text-gray-600 dark:text-gray-400 font-mono" title={referralCode.inviteLink}>
                {formatInviteLink(referralCode.inviteLink)}
              </span>
            </div>
            <button
              onClick={() => onCopyLink(referralCode.inviteLink)}
              className="px-2 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded transition-colors duration-200"
              title="复制邀请链接"
            >
              <Copy className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      {/* 邀请码信息 - 紧凑显示 */}
      <div className="mb-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>创建时间: {new Date(referralCode.createdAt).toLocaleDateString()}</span>
          <div className="flex items-center space-x-1">
            <div className={`w-1.5 h-1.5 rounded-full ${referralCode.isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>{referralCode.isActive ? '有效' : '已失效'}</span>
          </div>
        </div>
      </div>


    </div>
  );
};

export default InviteCodeGenerator;
