import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import { rewardService } from '../services/rewardService';

const router = express.Router();

interface RedeemRequestBody {
  code: string;
}

/**
 * 兑换码兑换
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { code } = req.body as RedeemRequestBody;

    if (!code || code.trim() === '') {
      return res.status(400).json({ 
        success: false,
        message: '兑换码不能为空' 
      });
    }

    // 使用事务确保原子性
    const result = await prisma.$transaction(async (tx) => {
      const redemptionCode = await tx.redemptionCode.findUnique({
        where: { code: code.trim() },
      });

      if (!redemptionCode) {
        throw new Error('兑换码无效或不存在');
      }

      if (redemptionCode.isUsed) {
        throw new Error('此兑换码已被使用');
      }

      if (!redemptionCode.isActive) {
        throw new Error('此兑换码已被禁用');
      }

      if (redemptionCode.expiresAt && new Date() > new Date(redemptionCode.expiresAt)) {
        throw new Error('此兑换码已过期');
      }

      if (redemptionCode.usageCount >= redemptionCode.usageLimit) {
        throw new Error('此兑换码使用次数已达上限');
      }

      // 更新兑换码使用次数
      const updatedCode = await tx.redemptionCode.update({
        where: { id: redemptionCode.id },
        data: {
          usageCount: {
            increment: 1
          },
          isUsed: redemptionCode.usageCount + 1 >= redemptionCode.usageLimit,
          userId: redemptionCode.usageCount + 1 >= redemptionCode.usageLimit ? userId : redemptionCode.userId,
        },
      });

      // 记录使用历史
      const codeUsageRecord = await tx.codeUsage.create({
        data: {
          codeId: redemptionCode.id,
          userId: userId,
          benefitType: redemptionCode.benefitType,
          benefitAmount: redemptionCode.benefitValue
        }
      });

      // 根据兑换码类型应用权益
      // 确保用户有 UserBalance 记录
      let userBalance = await tx.userBalance.findUnique({
        where: { userId: userId },
      });

      if (!userBalance) {
        // 如果不存在，创建一个默认记录
        userBalance = await tx.userBalance.create({
          data: {
            userId: userId,
            mockInterviewCredits: 0,
            formalInterviewCredits: 0,
            mianshijunBalance: 0,
          },
        });
      }

      // 根据不同的权益类型更新对应字段
      if (updatedCode.benefitType === 'POINTS') {
        // 更新面巾余额
        userBalance = await tx.userBalance.update({
          where: { userId: userId },
          data: {
            mianshijunBalance: {
              increment: updatedCode.benefitValue,
            },
          },
        });
      } else if (updatedCode.benefitType === 'MOCK_INTERVIEW') {
        // 更新模拟面试次数
        userBalance = await tx.userBalance.update({
          where: { userId: userId },
          data: {
            mockInterviewCredits: {
              increment: updatedCode.benefitValue,
            },
          },
        });
      } else if (updatedCode.benefitType === 'FORMAL_INTERVIEW') {
        // 更新正式面试次数
        userBalance = await tx.userBalance.update({
          where: { userId: userId },
          data: {
            formalInterviewCredits: {
              increment: updatedCode.benefitValue,
            },
          },
        });
      } else {
        // 处理未知类型的权益
        console.warn(`Unhandled benefit type: ${updatedCode.benefitType} for code ${updatedCode.code}`);
      }

      return { updatedCode, userBalance, codeUsageId: codeUsageRecord.id };
    });

    // 异步处理邀请奖励（不阻塞响应）
    setImmediate(async () => {
      try {
        const rewardResult = await rewardService.processReferralRewardForCodeUsage(result.codeUsageId);
        if (rewardResult.rewardProcessed) {
          console.log(`🎉 邀请奖励处理成功: ${rewardResult.message}`);
        }
      } catch (error) {
        console.error('处理邀请奖励时出错:', error);
      }
    });

    // 根据权益类型返回相应的余额信息
    let newBalance = 0;
    if (result.updatedCode.benefitType === 'POINTS') {
      newBalance = result.userBalance?.mianshijunBalance || 0;
    } else if (result.updatedCode.benefitType === 'MOCK_INTERVIEW') {
      newBalance = result.userBalance?.mockInterviewCredits || 0;
    } else if (result.updatedCode.benefitType === 'FORMAL_INTERVIEW') {
      newBalance = result.userBalance?.formalInterviewCredits || 0;
    }

    return res.status(200).json({
      success: true,
      message: '兑换成功！',
      benefitType: result.updatedCode.benefitType,
      benefitValue: result.updatedCode.benefitValue,
      newBalance: newBalance,
    });

  } catch (error: any) {
    console.error('兑换处理失败:', error);

    // 处理数据库连接错误
    if (error.message.includes('terminating connection') ||
        error.message.includes('connection') ||
        error.code === 'P1001' || // Prisma connection error
        error.code === 'P1017') { // Prisma connection timeout
      console.log('🔄 数据库连接问题，尝试重新连接...');
      try {
        await prisma.$connect();
        return res.status(503).json({ 
          success: false,
          message: '数据库连接已恢复，请重试' 
        });
      } catch (reconnectError) {
        return res.status(503).json({ 
          success: false,
          message: '数据库连接失败，请稍后再试' 
        });
      }
    }

    // 处理业务逻辑错误
    if (error.message.includes('兑换码无效') ||
        error.message.includes('已被使用') ||
        error.message.includes('已过期')) {
      return res.status(400).json({ 
        success: false,
        message: error.message 
      });
    }

    // 处理其他内部错误
    return res.status(500).json({ 
      success: false,
      message: '兑换失败，请稍后再试' 
    });
  }
});

export default router;
