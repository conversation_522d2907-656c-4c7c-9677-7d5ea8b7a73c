import './utils/logger';
// 启用后端日志系统
import './utils/backendLogSaver.js';
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http'; // 导入 createServer
import interviewRoutes from './interviews'; // 导入面试路由






import { setupWebSocket } from './websocket/interviewWs'; // 导入WebSocket设置函数
import prisma from './lib/prisma'; // 使用共享的Prisma实例

// 加载环境变量
dotenv.config();

// 测试数据库连接（带重试机制）
async function testDatabaseConnection(retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      await prisma.$connect();
      console.log('✅ 数据库连接成功');
      return true;
    } catch (error) {
      console.error(`❌ 数据库连接失败 (尝试 ${i + 1}/${retries}):`, error);
      if (i < retries - 1) {
        console.log('⏳ 等待2秒后重试...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  return false;
}

const app = express();
const PORT = process.env.PORT || 3000; // 本地端口配置

// 中间件
app.use(cors()); // 允许跨域请求
app.use(express.json()); // 解析 JSON 请求体
app.use(express.text()); // 解析文本请求体（用于sendBeacon）

// 添加请求日志中间件
app.use((req: Request, res: Response, next: NextFunction) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// API 路由
// 认证相关API路由 (Authentication API Routes)
import authRoutes from './routes/auth';
app.use('/api/auth', authRoutes);

// 文件上传API路由 (File Upload API Route)
import { upload, authenticateMiddleware, uploadHandler } from './upload/resume';
// 使用multer中间件处理文件上传
app.post('/api/upload/resume',
  authenticateMiddleware,
  upload.single('file'),
  uploadHandler
);

// 简历API路由已迁移到业务API路由部分

// 意向岗位API路由已迁移到业务API路由部分

// 用户信息API路由 (Users API Route)
import usersRoutes from './routes/users';
app.use('/api/users', usersRoutes);

// 邀请相关API路由 (Referral API Routes) - 统一使用Express路由
import referralRoutes from './routes/referral';
app.use('/api/referral', referralRoutes);

// 反馈API路由已迁移到业务API路由部分

// 订单API路由 (Orders API Routes) - 统一使用Express路由
import ordersRoutes from './routes/orders';
app.use('/api/orders', ordersRoutes);

// 面试API路由 (Interview API Route)
app.use('/api/interviews', interviewRoutes);

// AI模拟面试记录API路由 (Mock Interview Records API Route)
import mockInterviewRoutes from './routes/mockInterviewRoutes.js';
app.use('/api/mock-interviews', mockInterviewRoutes);

// 性能监控API路由 (Performance Monitoring API Route)
import performanceRoutes from './routes/performance';
app.use('/api/performance', performanceRoutes);

// 日志保存API路由 (Log Saving API Route)
import logRoutes from './routes/logs';
app.use('/api/logs', logRoutes);

// 系统API路由 (System API Routes) - 统一使用Express路由
import systemRoutes from './routes/system';
app.use('/api/system', systemRoutes);
app.use('/api', systemRoutes); // 兼容原有的 /api/frontend-logs 路径

// 兑换码API路由 (Redeem API Routes) - 统一使用Express路由
import redeemRoutes from './routes/redeem';
app.use('/api/redeem', redeemRoutes);

// 消费记录API路由 (Usage Records API Routes) - 统一使用Express路由
import usageRecordsRoutes from './routes/usage-records';
app.use('/api/usage-records', usageRecordsRoutes);

// 扣费API路由 (Credits API Routes) - 统一使用Express路由
import creditsRoutes from './routes/credits';
app.use('/api/credits', creditsRoutes);

// 管理员兑换码API路由 (Admin Redemption Codes API Routes) - 统一使用Express路由
import adminRedemptionCodesRoutes from './routes/admin/redemption-codes';
app.use('/api/admin/redemption-codes', adminRedemptionCodesRoutes);

// 管理员统计报表API路由 (Admin Stats API Routes) - 统一使用Express路由
import adminRedemptionStatsRoutes from './routes/admin/redemption-stats';
app.use('/api/admin/redemption-stats', adminRedemptionStatsRoutes);

// 管理员通知管理API路由 (Admin Notifications API Routes) - 统一使用Express路由
import adminNotificationsRoutes from './routes/admin/notifications';
app.use('/api/admin/notifications', adminNotificationsRoutes);

// 用户通知API路由 (User Notifications API Routes) - 统一使用Express路由
import notificationsRoutes from './routes/notifications';
app.use('/api/notifications', notificationsRoutes);

// 业务API路由 (Business API Routes) - 统一使用Express路由
import resumesRoutes from './routes/resumes';
import positionsRoutes from './routes/positions';
import feedbackRoutes from './routes/feedback';
app.use('/api/resumes', resumesRoutes);
app.use('/api/positions', positionsRoutes);
app.use('/api/feedback', feedbackRoutes);

// 根路径健康检查 (可选)
app.get('/', (req: Request, res: Response) => {
  res.status(200).json({ message: 'Backend server is running locally', timestamp: new Date().toISOString() });
});

// 添加全局错误处理中间件
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('服务器错误:', err);

  // 检查是否是数据库连接错误
  if (err.message.includes('terminating connection') ||
      err.message.includes('connection') ||
      err.message.includes('ECONNREFUSED')) {
    console.log('🔄 检测到数据库连接问题，尝试重新连接...');
    // 尝试重新连接数据库
    testDatabaseConnection().catch(console.error);
  }

  res.status(500).json({
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? err.message : '请稍后再试'
  });
});

// 为了处理不存在的路由
app.use((req: Request, res: Response) => {
  res.status(404).json({ message: '请求的资源不存在' });
});

// 启动服务器并处理优雅关闭
async function startServer() {
  try {
    // 首先测试数据库连接
    const isConnected = await testDatabaseConnection();
    if (!isConnected) {
      console.warn('警告: 数据库连接失败，但服务器仍将启动');
    }

    // --- 创建 HTTP 服务器并集成 WebSocket 服务器 ---
    // --- Create HTTP server and integrate WebSocket server ---
    const server = createServer(app); // 使用 Express app 创建 HTTP 服务器

    // 调用 setupWebSocket 并传入 server 实例
    // Call setupWebSocket and pass the server instance
    setupWebSocket(server);

    // 添加服务器错误处理
    server.on('error', (error: any) => {
      console.error('服务器错误:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`端口 ${PORT} 已被占用，请检查是否有其他服务在运行`);
        process.exit(1);
      }
    });

    // 启动服务器
    server.listen(PORT, () => {
      console.log(`\n✅ Express server with WebSocket support running on http://localhost:${PORT}`);
      console.log(`健康检查地址: http://localhost:${PORT}/`);
      console.log(`注册端点: http://localhost:${PORT}/api/auth/register`);
      console.log(`登录端点: http://localhost:${PORT}/api/auth/login`);
      console.log(`WebSocket连接将通过HTTP服务器升级处理\n`);
    });

    // 添加全局错误处理
    process.on('uncaughtException', (error) => {
      console.error('未捕获的异常:', error);
      gracefulShutdown(server);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('未处理的Promise拒绝:', reason);
      gracefulShutdown(server);
    });

    // 处理进程终止信号
    process.on('SIGTERM', () => gracefulShutdown(server));
    process.on('SIGINT', () => gracefulShutdown(server));

    return server;
  } catch (error) {
    console.error('启动服务器时出错:', error);
    process.exit(1);
  }
}

// 优雅关闭函数
function gracefulShutdown(server: any) {
  console.log('正在优雅关闭服务器...');
  server.close(async () => {
    console.log('服务器已关闭');
    try {
      await prisma.$disconnect();
      console.log('数据库连接已断开');
    } catch (err) {
      console.error('断开数据库连接时出错:', err);
    }
    process.exit(0);
  });

  // 10秒后强制退出
  setTimeout(() => {
    console.error('无法优雅关闭，强制退出');
    process.exit(1);
  }, 10000);
}

// 启动服务器
startServer();