# 项目上下文信息

- AI模拟面试功能重构需求：新增开始按钮和实时对话流程，使用LLM实现面试官-用户问答循环，严格保持现有UI不变
- 用户要求输出git提交命令进行手动提交，需要使用"开发版："前缀，并完整记录当前的改动内容。当前正在进行Vercel代码清理工作，准备移除backend/api/referral/目录并统一使用Express路由架构。
- 后端服务器启动问题解决方案：当前端出现500错误且无法连接API时，首先检查后端服务器是否在运行。使用 `netstat -ano | findstr ":3000" | findstr "LISTENING"` 检查端口状态，如果没有LISTENING状态则需要启动后端服务器。在backend目录下使用 `npx tsx watch server.ts` 启动开发服务器，确保数据库连接和WebSocket服务正常初始化。
- 修复分享有礼页面邀请码和邀请链接复制功能Toast提示不显示问题：将ReferralRewardsPage.tsx中的useToast hook改为useToastContext，修复了Toast状态隔离导致的显示问题，现在复制按钮点击后能正常显示绿色成功Toast提示
- 修复管理系统Toast进度条问题：1.修复ToastContext.tsx中ToastContainer缺少toasts和onRemove参数传递的问题 2.将Toast.tsx中复杂的JavaScript+requestAnimationFrame进度条实现改为简单可靠的CSS动画方式，与主系统保持一致
- 通知系统测试已完成：管理后台成功创建通知"测试通知功能"，通知被正确保存到数据库，前端成功从API获取并显示通知列表，createUserNotifications函数被正确调用。用户已登录主系统准备测试通知接收功能。
- 通知管理系统开发完成：实现了完整的管理后台通知CRUD功能、用户通知接收机制、主系统通知显示界面，包括数据库schema设计(Notification/NotificationTarget/UserNotification表)、后端API路由、前端管理界面和通知下拉组件，已通过完整测试验证功能正常
