import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { referralService } from '../services/referralService';

const router = express.Router();

// 邀请码接口
interface ReferralCode {
  code: string;
  inviteLink: string;
  createdAt: string;
  isActive: boolean;
  hasInviter: boolean;
}

// 邀请统计接口
interface ReferralStats {
  totalInvited: number;
  successfulReferrals: number;
  totalRewards: number;
  pendingRewards: number;
}

/**
 * 获取或生成用户邀请码
 */
router.get('/code', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const referralCode = await referralService.getOrCreateReferralCode(userId);
    const hasInviter = await referralService.checkExistingReferralRelation(userId);

    return res.status(200).json({
      success: true,
      data: {
        code: referralCode.code,
        inviteLink: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?invite=${referralCode.code}`,
        createdAt: referralCode.createdAt,
        isActive: referralCode.isActive,
        hasInviter
      }
    });

  } catch (error: any) {
    console.error('获取邀请码失败:', error);
    return res.status(500).json({
      success: false,
      message: error.message || '获取邀请码失败'
    });
  }
});

/**
 * 获取用户邀请统计数据
 */
router.get('/stats', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const stats = await referralService.getReferralStats(userId);

    return res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('获取邀请统计失败:', error);
    return res.status(500).json({
      success: false,
      message: error.message || '获取统计数据失败'
    });
  }
});

/**
 * 获取用户邀请记录
 */
router.get('/history', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    // 解析查询参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
    const status = (req.query.status as string) || 'all';

    // 验证状态参数
    const validStatuses = ['all', 'registered', 'paid', 'rewarded'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态参数'
      });
    }

    const options = {
      page,
      limit,
      status: status as 'all' | 'registered' | 'paid' | 'rewarded'
    };

    const history = await referralService.getReferralHistory(userId, options);

    return res.status(200).json({
      success: true,
      data: history
    });

  } catch (error: any) {
    console.error('获取邀请记录失败:', error);
    return res.status(500).json({
      success: false,
      message: error.message || '获取邀请记录失败'
    });
  }
});

/**
 * 验证邀请码有效性
 */
router.post('/code/validate', async (req: Request, res: Response) => {
  try {
    const { code } = req.body;

    if (!code || code.trim() === '') {
      return res.status(400).json({
        success: false,
        valid: false,
        message: '邀请码不能为空'
      });
    }

    const isValid = await referralService.validateReferralCode(code);

    return res.status(200).json({
      success: true,
      valid: isValid,
      message: isValid ? '邀请码有效' : '邀请码无效或已过期'
    });

  } catch (error: any) {
    console.error('验证邀请码失败:', error);
    return res.status(500).json({
      success: false,
      valid: false,
      message: '验证失败，请稍后重试'
    });
  }
});

/**
 * 设置邀请人邀请码（注册后补填）
 */
router.post('/code/set-inviter', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { inviterCode } = req.body;

    if (!inviterCode || inviterCode.trim() === '') {
      return res.status(400).json({
        success: false,
        message: '邀请码不能为空'
      });
    }

    // 检查用户是否已有邀请关系
    const existingRelation = await referralService.checkExistingReferralRelation(userId);
    if (existingRelation) {
      return res.status(400).json({
        success: false,
        message: '您已经填写过邀请人邀请码，不能重复填写'
      });
    }

    // 验证邀请码有效性
    const isValid = await referralService.validateReferralCode(inviterCode.trim());
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: '邀请码无效或已过期'
      });
    }

    // 创建邀请关系
    await referralService.createReferralRelation(inviterCode.trim(), userId);

    return res.status(200).json({
      success: true,
      message: '邀请人邀请码设置成功'
    });

  } catch (error: any) {
    console.error('设置邀请人邀请码失败:', error);
    return res.status(500).json({
      success: false,
      message: error.message || '设置失败，请稍后重试'
    });
  }
});

export default router;
